#!/usr/bin/env node

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

const OBSIDIAN_PATH = '/Applications/Obsidian.app/Contents/MacOS/Obsidian';
const USER_DATA_DIR = path.join(process.cwd(), 'e2e/test_obsidian_data');
const DEBUG_PORT = 9222;
const HEALTH_CHECK_URL = `http://127.0.0.1:${DEBUG_PORT}/json`;

let obsidianProcess = null;

async function sleep(seconds) {
    return new Promise(resolve => setTimeout(resolve, seconds * 1000));
}

async function checkObsidianReady() {
    try {
        const response = await fetch(HEALTH_CHECK_URL);
        const data = await response.json();
        return Array.isArray(data) && data.length > 0;
    } catch (error) {
        return false;
    }
}

async function waitForObsidian(maxWaitSeconds = 30) {
    console.log('⏳ Waiting for Obsidian to be ready...');

    for (let i = 0; i < maxWaitSeconds; i++) {
        if (await checkObsidianReady()) {
            console.log('✅ Obsidian is ready!');
            return true;
        }
        process.stdout.write('.');
        await sleep(1);
    }

    console.log('\n❌ Obsidian failed to start within timeout');
    return false;
}

function buildPlugin() {
    return new Promise((resolve, reject) => {
        console.log('🔨 Building plugin...');
        exec('npm run build', (error, stdout, stderr) => {
            if (error) {
                console.error('❌ Build failed:', error);
                reject(error);
            } else {
                console.log('✅ Plugin built successfully');
                resolve();
            }
        });
    });
}

function copyPluginFiles() {
    return new Promise((resolve, reject) => {
        console.log('📁 Copying plugin files to test vault...');
        exec('cp main.js manifest.json styles.css tests/vault/Test/.obsidian/plugins/ghost-sync/', (error, stdout, stderr) => {
            if (error) {
                console.error('❌ Copy failed:', error);
                reject(error);
            } else {
                console.log('✅ Plugin files copied');
                resolve();
            }
        });
    });
}

function startObsidian() {
    return new Promise((resolve, reject) => {
        console.log('🚀 Starting Obsidian...');

        obsidianProcess = spawn(OBSIDIAN_PATH, [
            `--user-data-dir=${USER_DATA_DIR}`,
            `--remote-debugging-port=${DEBUG_PORT}`
        ], {
            stdio: 'pipe',
            detached: false
        });

        obsidianProcess.stdout.on('data', (data) => {
            const output = data.toString();
            if (output.includes('DevTools listening')) {
                console.log('✅ Obsidian started with DevTools');
                resolve();
            }
        });

        obsidianProcess.stderr.on('data', (data) => {
            // Ignore stderr for now as Obsidian outputs some warnings
        });

        obsidianProcess.on('error', (error) => {
            console.error('❌ Failed to start Obsidian:', error);
            reject(error);
        });

        obsidianProcess.on('exit', (code) => {
            if (code !== 0) {
                console.log(`⚠️  Obsidian exited with code ${code}`);
            }
        });

        // Fallback: resolve after 3 seconds even if we don't see the DevTools message
        setTimeout(() => {
            console.log('⏳ Obsidian should be starting...');
            resolve();
        }, 3000);
    });
}

function runTests() {
    return new Promise((resolve, reject) => {
        console.log('🧪 Running e2e tests...');

        const testProcess = spawn('npx', ['mocha', 'e2e/specs/create-new-post.e2e.ts', '--require', 'ts-node/register', '--timeout', '120000'], {
            stdio: 'inherit'
        });

        testProcess.on('exit', (code) => {
            if (code === 0) {
                console.log('✅ All tests passed!');
                resolve();
            } else {
                console.log(`❌ Tests failed with exit code ${code}`);
                reject(new Error(`Tests failed with exit code ${code}`));
            }
        });

        testProcess.on('error', (error) => {
            console.error('❌ Failed to run tests:', error);
            reject(error);
        });
    });
}

function cleanup() {
    if (obsidianProcess) {
        console.log('🧹 Cleaning up Obsidian process...');
        obsidianProcess.kill('SIGTERM');

        // Force kill after 5 seconds if it doesn't exit gracefully
        setTimeout(() => {
            if (obsidianProcess && !obsidianProcess.killed) {
                console.log('🔪 Force killing Obsidian process...');
                obsidianProcess.kill('SIGKILL');
            }
        }, 5000);
    }
}

async function main() {
    try {
        // Setup
        await buildPlugin();
        await copyPluginFiles();

        // Start Obsidian
        await startObsidian();

        // Wait for it to be ready
        const isReady = await waitForObsidian();
        if (!isReady) {
            throw new Error('Obsidian failed to start');
        }

        // Run tests
        await runTests();

        console.log('🎉 E2E tests completed successfully!');

    } catch (error) {
        console.error('💥 E2E test run failed:', error.message);
        process.exit(1);
    } finally {
        cleanup();
        process.exit(0);
    }
}

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n⚠️  Received SIGINT, cleaning up...');
    cleanup();
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n⚠️  Received SIGTERM, cleaning up...');
    cleanup();
    process.exit(0);
});

if (require.main === module) {
    main();
}

module.exports = { main };
