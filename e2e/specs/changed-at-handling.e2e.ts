import { chromium } from 'playwright';
import type { <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';
import { assert } from 'chai';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Wait for async operations to complete with smart polling
 */
async function waitForAsyncOperation(timeout: number = 1000): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, timeout));
}

/**
 * Helper to access plugin sync metadata via Obsidian
 */
async function getSyncMetadata(page: Page, filePath: string): Promise<any> {
  return await page.evaluate((path) => {
    const plugin = (window as any).app.plugins.plugins['ghost-sync'];
    if (!plugin || !plugin.syncMetadata) {
      throw new Error('Ghost sync plugin or syncMetadata not found');
    }

    // Get the TFile object
    const file = (window as any).app.vault.getAbstractFileByPath(path);
    if (!file) {
      throw new Error(`File not found: ${path}`);
    }

    return plugin.syncMetadata.getMetadata(file);
  }, filePath);
}

/**
 * Helper to get changed_at timestamp for a file
 */
async function getChangedAt(page: Page, filePath: string): Promise<string | undefined> {
  const metadata = await getSyncMetadata(page, filePath);
  return metadata.changed_at;
}

/**
 * Helper to create a test file with specific content
 */
function createTestFile(filePath: string, content: string): void {
  const dir = path.dirname(filePath);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  fs.writeFileSync(filePath, content);
}

/**
 * Helper to modify a test file
 */
function modifyTestFile(filePath: string, content: string): void {
  fs.writeFileSync(filePath, content);
}

describe("Ghost Sync - changed_at Handling E2E Tests", function () {
  this.timeout(30 * 1000);

  let browser: Browser;
  let page: Page;
  const articlesDir = path.join(__dirname, '../../tests/vault/Test/articles');

  before(async function () {
    // Connect to existing Obsidian instance via CDP
    browser = await chromium.connectOverCDP('http://127.0.0.1:9222');

    // Get the first page (Obsidian window)
    const contexts = browser.contexts();
    const context = contexts[0];
    const pages = context.pages();
    page = pages[0];

    console.log("Connected to Obsidian via Playwright");

    // Enable the ghost-sync plugin
    await page.evaluate(() => {
      (window as any).app.plugins.setEnable(true);
      (window as any).app.plugins.enablePlugin('ghost-sync');
    });

    // Wait for plugin to be ready
    await waitForAsyncOperation(1000);
  });

  after(async function () {
    if (browser) {
      await browser.close();
    }
  });

  beforeEach(async function () {
    // Clear any existing files in the articles directory
    if (fs.existsSync(articlesDir)) {
      const files = fs.readdirSync(articlesDir);
      for (const file of files) {
        if (file.endsWith('.md')) {
          fs.unlinkSync(path.join(articlesDir, file));
        }
      }
    }

    // Wait for file system operations to complete
    await waitForAsyncOperation(500);
  });

  it("should set changed_at when creating a new post with slug", async function () {
    const testTitle = "Test Changed At Post";
    const testSlug = "test-changed-at-post";
    const filePath = path.join(articlesDir, `${testSlug}.md`);
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create a test file with frontmatter including a slug
    const content = `---
Title: "${testTitle}"
Slug: "${testSlug}"
Status: "draft"
Featured Image: null
Newsletter: null
---

# ${testTitle}

This is test content for changed_at handling.`;

    createTestFile(filePath, content);

    // Open the file in Obsidian to trigger the sync status view
    await page.evaluate((path) => {
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (file) {
        (window as any).app.workspace.getLeaf().openFile(file);
      }
    }, relativeFilePath);

    // Wait for file modification detection to process
    await waitForAsyncOperation(2000);

    // Check that changed_at was set
    const changedAt = await getChangedAt(page, relativeFilePath);

    assert(changedAt, 'changed_at should be set for new file with slug');
    assert(new Date(changedAt).getTime() > 0, 'changed_at should be a valid timestamp');

    console.log(`✅ changed_at set for new file: ${changedAt}`);
  });

  it("should update changed_at when sync-relevant content changes", async function () {
    const testSlug = "test-sync-relevant-changes";
    const filePath = path.join(articlesDir, `${testSlug}.md`);
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create initial file
    const initialContent = `---
Title: "Initial Title"
Slug: "${testSlug}"
Status: "draft"
---

Initial content.`;

    createTestFile(filePath, initialContent);

    // Open the file in Obsidian
    await page.evaluate((path) => {
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (file) {
        (window as any).app.workspace.getLeaf().openFile(file);
      }
    }, relativeFilePath);

    await waitForAsyncOperation(2000);

    // Get initial changed_at
    const initialChangedAt = await getChangedAt(page, relativeFilePath);
    assert(initialChangedAt, 'Initial changed_at should be set');

    // Wait a bit to ensure timestamp difference
    await waitForAsyncOperation(1000);

    // Modify sync-relevant content (title)
    const modifiedContent = `---
Title: "Modified Title"
Slug: "${testSlug}"
Status: "draft"
---

Modified content with new information.`;

    modifyTestFile(filePath, modifiedContent);

    // Trigger file reload in Obsidian
    await page.evaluate((path) => {
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (file) {
        // Trigger a vault modify event
        (window as any).app.vault.trigger('modify', file);
      }
    }, relativeFilePath);

    await waitForAsyncOperation(2000);

    // Check that changed_at was updated
    const updatedChangedAt = await getChangedAt(page, relativeFilePath);

    assert(updatedChangedAt, 'changed_at should still be set after modification');
    assert(updatedChangedAt !== initialChangedAt, 'changed_at should be updated after sync-relevant changes');
    assert(new Date(updatedChangedAt).getTime() > new Date(initialChangedAt).getTime(),
           'Updated changed_at should be newer than initial');

    console.log(`✅ changed_at updated: ${initialChangedAt} -> ${updatedChangedAt}`);
  });

  it("should NOT update changed_at for non-sync-relevant changes", async function () {
    const testSlug = "test-non-sync-changes";
    const filePath = path.join(articlesDir, `${testSlug}.md`);
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create initial file
    const initialContent = `---
Title: "Test Non-Sync Changes"
Slug: "${testSlug}"
Status: "draft"
CustomProperty: "not synced"
---

Initial content.`;

    createTestFile(filePath, initialContent);

    // Open the file in Obsidian
    await page.evaluate((path) => {
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (file) {
        (window as any).app.workspace.getLeaf().openFile(file);
      }
    }, relativeFilePath);

    await waitForAsyncOperation(2000);

    // Get initial changed_at
    const initialChangedAt = await getChangedAt(page, relativeFilePath);
    assert(initialChangedAt, 'Initial changed_at should be set');

    // Wait a bit to ensure timestamp difference would be detectable
    await waitForAsyncOperation(1000);

    // Modify only non-sync-relevant content (custom property and comments)
    const modifiedContent = `---
Title: "Test Non-Sync Changes"
Slug: "${testSlug}"
Status: "draft"
CustomProperty: "modified but not synced"
---

Initial content.
<!-- This is a comment that should not trigger sync -->`;

    modifyTestFile(filePath, modifiedContent);

    // Trigger file reload in Obsidian
    await page.evaluate((path) => {
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (file) {
        (window as any).app.vault.trigger('modify', file);
      }
    }, relativeFilePath);

    await waitForAsyncOperation(2000);

    // Check that changed_at was NOT updated
    const unchangedChangedAt = await getChangedAt(page, relativeFilePath);

    assert(unchangedChangedAt === initialChangedAt,
           'changed_at should NOT be updated for non-sync-relevant changes');

    console.log(`✅ changed_at unchanged for non-sync changes: ${unchangedChangedAt}`);
  });

  it("should ignore files without slugs", async function () {
    const fileName = "no-slug-file.md";
    const filePath = path.join(articlesDir, fileName);
    const relativeFilePath = `articles/${fileName}`;

    // Create a file without a slug
    const content = `---
Title: "File Without Slug"
Status: "draft"
---

This file has no slug and should be ignored.`;

    createTestFile(filePath, content);

    // Open the file in Obsidian
    await page.evaluate((path) => {
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (file) {
        (window as any).app.workspace.getLeaf().openFile(file);
      }
    }, relativeFilePath);

    await waitForAsyncOperation(2000);

    // Check that changed_at was NOT set
    const changedAt = await getChangedAt(page, relativeFilePath);

    assert(!changedAt, 'changed_at should NOT be set for files without slugs');

    console.log(`✅ Files without slugs are ignored`);
  });

  it("should handle multiple rapid modifications correctly", async function () {
    const testSlug = "test-rapid-modifications";
    const filePath = path.join(articlesDir, `${testSlug}.md`);
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create initial file
    const initialContent = `---
Title: "Test Rapid Modifications"
Slug: "${testSlug}"
Status: "draft"
---

Initial content.`;

    createTestFile(filePath, initialContent);

    // Open the file in Obsidian
    await page.evaluate((path) => {
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (file) {
        (window as any).app.workspace.getLeaf().openFile(file);
      }
    }, relativeFilePath);

    await waitForAsyncOperation(2000);

    // Get initial changed_at
    const initialChangedAt = await getChangedAt(page, relativeFilePath);
    assert(initialChangedAt, 'Initial changed_at should be set');

    // Make multiple rapid modifications
    for (let i = 1; i <= 3; i++) {
      const modifiedContent = `---
Title: "Test Rapid Modifications ${i}"
Slug: "${testSlug}"
Status: "draft"
---

Modified content iteration ${i}.`;

      modifyTestFile(filePath, modifiedContent);

      // Trigger file reload in Obsidian
      await page.evaluate((path) => {
        const file = (window as any).app.vault.getAbstractFileByPath(path);
        if (file) {
          (window as any).app.vault.trigger('modify', file);
        }
      }, relativeFilePath);

      // Small delay between modifications
      await waitForAsyncOperation(300);
    }

    // Wait for all modifications to be processed
    await waitForAsyncOperation(3000);

    // Check that changed_at was updated
    const finalChangedAt = await getChangedAt(page, relativeFilePath);

    assert(finalChangedAt, 'changed_at should still be set after rapid modifications');
    assert(finalChangedAt !== initialChangedAt, 'changed_at should be updated after rapid modifications');
    assert(new Date(finalChangedAt).getTime() > new Date(initialChangedAt).getTime(),
           'Final changed_at should be newer than initial');

    console.log(`✅ Rapid modifications handled: ${initialChangedAt} -> ${finalChangedAt}`);
  });

  it("should persist changed_at in sync metadata storage", async function () {
    const testSlug = "test-persistence";
    const filePath = path.join(articlesDir, `${testSlug}.md`);
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create initial file
    const content = `---
Title: "Test Persistence"
Slug: "${testSlug}"
Status: "draft"
---

Content for persistence test.`;

    createTestFile(filePath, content);

    // Open the file in Obsidian
    await page.evaluate((path) => {
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (file) {
        (window as any).app.workspace.getLeaf().openFile(file);
      }
    }, relativeFilePath);

    await waitForAsyncOperation(2000);

    // Get changed_at
    const changedAt = await getChangedAt(page, relativeFilePath);
    assert(changedAt, 'changed_at should be set');

    // Verify it's stored in plugin data (not frontmatter)
    const pluginData = await page.evaluate(() => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      return plugin.loadData();
    });

    const syncMetadata = (await pluginData)?.['sync-metadata'];
    assert(syncMetadata, 'Sync metadata should exist in plugin data');
    assert(syncMetadata[relativeFilePath], 'File should have sync metadata entry');
    assert(syncMetadata[relativeFilePath].changed_at === changedAt,
           'changed_at should match between API and storage');

    console.log(`✅ changed_at persisted in sync metadata: ${changedAt}`);
  });

  it("should handle whitespace-only changes as non-sync-relevant", async function () {
    const testSlug = "test-whitespace-changes";
    const filePath = path.join(articlesDir, `${testSlug}.md`);
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create initial file
    const initialContent = `---
Title: "Test Whitespace Changes"
Slug: "${testSlug}"
Status: "draft"
---

Content without extra whitespace.`;

    createTestFile(filePath, initialContent);

    // Open the file in Obsidian
    await page.evaluate((path) => {
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (file) {
        (window as any).app.workspace.getLeaf().openFile(file);
      }
    }, relativeFilePath);

    await waitForAsyncOperation(2000);

    // Get initial changed_at
    const initialChangedAt = await getChangedAt(page, relativeFilePath);
    assert(initialChangedAt, 'Initial changed_at should be set');

    // Wait a bit to ensure timestamp difference would be detectable
    await waitForAsyncOperation(1000);

    // Add only whitespace changes
    const modifiedContent = `---
Title: "Test Whitespace Changes"
Slug: "${testSlug}"
Status: "draft"
---

Content without extra whitespace.

   `;

    modifyTestFile(filePath, modifiedContent);

    // Trigger file reload in Obsidian
    await page.evaluate((path) => {
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (file) {
        (window as any).app.vault.trigger('modify', file);
      }
    }, relativeFilePath);

    await waitForAsyncOperation(2000);

    // Check that changed_at was NOT updated (content is trimmed in hash calculation)
    const unchangedChangedAt = await getChangedAt(page, relativeFilePath);

    assert(unchangedChangedAt === initialChangedAt,
           'changed_at should NOT be updated for whitespace-only changes');

    console.log(`✅ Whitespace-only changes ignored: ${unchangedChangedAt}`);
  });

  it("should integrate with Ghost sync status view", async function () {
    const testSlug = "test-sync-status-integration";
    const filePath = path.join(articlesDir, `${testSlug}.md`);
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create initial file
    const content = `---
Title: "Test Sync Status Integration"
Slug: "${testSlug}"
Status: "draft"
---

Content for sync status integration test.`;

    createTestFile(filePath, content);

    // Open the file in Obsidian
    await page.evaluate((path) => {
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (file) {
        (window as any).app.workspace.getLeaf().openFile(file);
      }
    }, relativeFilePath);

    // Open the Ghost sync status view
    await page.evaluate(() => {
      (window as any).app.commands.executeCommandById('ghost-sync:open-sync-status');
    });

    await waitForAsyncOperation(3000);

    // Verify the Ghost tab is visible and shows sync status
    const ghostTabExists = await page.evaluate(() => {
      const leaves = (window as any).app.workspace.getLeavesOfType('ghost-sync-status');
      return leaves.length > 0;
    });

    assert(ghostTabExists, 'Ghost sync status view should be open');

    // Get changed_at from sync metadata
    const changedAt = await getChangedAt(page, relativeFilePath);
    assert(changedAt, 'changed_at should be set when file is opened in sync status view');

    // Modify the file content
    const modifiedContent = `---
Title: "Test Sync Status Integration - Modified"
Slug: "${testSlug}"
Status: "draft"
---

Modified content for sync status integration test.`;

    modifyTestFile(filePath, modifiedContent);

    // Trigger file modification event
    await page.evaluate((path) => {
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (file) {
        (window as any).app.vault.trigger('modify', file);
      }
    }, relativeFilePath);

    await waitForAsyncOperation(3000);

    // Verify changed_at was updated
    const updatedChangedAt = await getChangedAt(page, relativeFilePath);
    assert(updatedChangedAt !== changedAt, 'changed_at should be updated after modification');

    console.log(`✅ Sync status integration working: ${changedAt} -> ${updatedChangedAt}`);
  });

  it("should handle files with different slug formats", async function () {
    const testCases = [
      { slug: "simple-slug", title: "Simple Slug Test" },
      { slug: "slug_with_underscores", title: "Slug With Underscores" },
      { slug: "slug-with-numbers-123", title: "Slug With Numbers" },
      { slug: "UPPERCASE-SLUG", title: "Uppercase Slug Test" }
    ];

    for (const testCase of testCases) {
      const filePath = path.join(articlesDir, `${testCase.slug}.md`);
      const relativeFilePath = `articles/${testCase.slug}.md`;

      // Create file with specific slug format
      const content = `---
Title: "${testCase.title}"
Slug: "${testCase.slug}"
Status: "draft"
---

Content for ${testCase.title}.`;

      createTestFile(filePath, content);

      // Open the file in Obsidian
      await page.evaluate((path) => {
        const file = (window as any).app.vault.getAbstractFileByPath(path);
        if (file) {
          (window as any).app.workspace.getLeaf().openFile(file);
        }
      }, relativeFilePath);

      await waitForAsyncOperation(1500);

      // Check that changed_at was set
      const changedAt = await getChangedAt(page, relativeFilePath);
      assert(changedAt, `changed_at should be set for slug format: ${testCase.slug}`);

      console.log(`✅ Slug format "${testCase.slug}" handled correctly: ${changedAt}`);
    }
  });
});
